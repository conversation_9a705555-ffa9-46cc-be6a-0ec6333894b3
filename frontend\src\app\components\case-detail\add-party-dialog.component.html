<h2 mat-dialog-title>添加案件当事人</h2>
<mat-dialog-content class="dialog-content">
  <!-- 搜索当事人 -->
  <mat-form-field appearance="outline" style="width: 100%;">
    <mat-label>搜索当事人</mat-label>
    <input type="text"
           matInput
           [(ngModel)]="searchTerm"
           (input)="searchParties()"
           placeholder="输入姓名/单位名/身份证号/手机号进行模糊搜索">
    <mat-icon matSuffix>search</mat-icon>
  </mat-form-field>

  <!-- 加载指示器居中显示 -->
  <div *ngIf="isLoading" class="loading-spinner-container">
    <mat-progress-spinner diameter="40" mode="indeterminate"></mat-progress-spinner>
    <p>正在加载当事人数据...</p>
  </div>

  <!-- 搜索结果区域，固定高度 -->
  <div class="search-results-container"
       [class.has-content]="searchTerm.length > 0"
       *ngIf="!isLoading">
    <!-- 搜索结果直接显示 -->
    <div class="search-results-list" *ngIf="searchTerm.length > 0">
      <div class="search-result-item"
           *ngFor="let party of filteredParties"
           (click)="selectPartyDirectly(party)">
        <span class="party-option">
          <span class="party-name">{{ party.name }}</span>
          <span class="party-type-badge" [ngClass]="party.type === 'natural' ? 'natural' : 'legal'">
            {{ party.type === 'natural' ? '自然人' : '单位' }}
          </span>
        </span>
        <small class="party-info">{{ party.info }}</small>
      </div>
      <div *ngIf="filteredParties.length === 0 && searchTerm.length > 0 && !isLoadingLegacy && legacyParties.length === 0" class="no-results">
        <span>无搜索结果，请尝试其他关键词</span>
      </div>
    </div>

    <!-- 历史案件搜索结果区域（不可选择） -->
    <div *ngIf="legacyParties.length > 0" class="legacy-results">
      <div class="legacy-header">
        <h4>历史案件匹配记录（仅供参考）</h4>
      </div>
      <div class="legacy-result-item" *ngFor="let party of legacyParties">
        <span class="party-option">
          <span class="party-name">{{ party.name }}</span>
          <span class="legacy-badge">
            {{ party.source }}
          </span>
        </span>
        <small class="party-info">案件编号: {{ party.contract_number }}</small>
        <small class="party-info">类型: {{ party.case_type }} | 立案日期: {{ party.filing_date | date:'yyyy-MM-dd' }}</small>
      </div>
    </div>

    <!-- 历史案件加载中 -->
    <div *ngIf="isLoadingLegacy" class="loading-spinner-container">
      <mat-progress-spinner diameter="30" mode="indeterminate"></mat-progress-spinner>
      <p>正在搜索历史案件数据...</p>
    </div>
  </div>

  <!-- 已选择的当事人 -->
  <div *ngIf="selectedParty" class="selected-party">
    <h3>已选择当事人</h3>
    <div class="party-details">
      <p><strong>名称:</strong> {{ selectedParty.name }}</p>
      <p><strong>类型:</strong> {{ selectedParty.type === 'natural' ? '自然人' : '单位' }}</p>
      <p><strong>相关信息:</strong> {{ selectedParty.info || '无' }}</p>
    </div>
  </div>

  <!-- 当事人角色选择 -->
  <div *ngIf="selectedParty" class="party-role-section">
    <mat-form-field appearance="outline" style="width: 100%;">
      <mat-label>当事人角色</mat-label>
      <mat-select [(ngModel)]="partyType" required>
        <!-- 根据案件类型显示不同选项 -->
        <ng-container *ngIf="caseType === '民事案件' || caseType === '行政案件'">
          <mat-option [value]="PartyType.PLAINTIFF">原告</mat-option>
          <mat-option [value]="PartyType.DEFENDANT">被告</mat-option>
          <mat-option [value]="PartyType.THIRD_PARTY">第三人</mat-option>
        </ng-container>

        <ng-container *ngIf="caseType === '刑事案件'">
          <mat-option [value]="PartyType.PLAINTIFF">受害人</mat-option>
          <mat-option [value]="PartyType.SUSPECT">犯罪嫌疑人</mat-option>
          <mat-option [value]="PartyType.SUSPECT_FAMILY">嫌疑人家属</mat-option>
        </ng-container>

        <ng-container *ngIf="caseType === '其他案件'">
          <mat-option [value]="PartyType.PLAINTIFF">原告</mat-option>
          <mat-option [value]="PartyType.DEFENDANT">被告</mat-option>
          <mat-option [value]="PartyType.THIRD_PARTY">第三人</mat-option>
          <mat-option [value]="PartyType.NON_LITIGATION_CLIENT">非诉委托人</mat-option>
        </ng-container>
      </mat-select>
    </mat-form-field>

    <mat-checkbox [(ngModel)]="isClient" class="client-checkbox">
      设为本案委托人
    </mat-checkbox>

    <mat-form-field appearance="outline" style="width: 100%;">
      <mat-label>内部编号(选填)</mat-label>
      <input matInput [(ngModel)]="internalNumber" placeholder="内部编号，可不填">
    </mat-form-field>

    <mat-form-field appearance="outline" style="width: 100%;">
      <mat-label>备注(选填)</mat-label>
      <textarea matInput [(ngModel)]="remarks" rows="3" placeholder="关于当事人的备注信息"></textarea>
    </mat-form-field>
  </div>
</mat-dialog-content>
<mat-dialog-actions align="end">
  <button mat-button [mat-dialog-close]="null">取消</button>
  <button mat-raised-button color="primary"
          [disabled]="!selectedParty || !partyType"
          [mat-dialog-close]="{
            id: selectedParty?.id,
            name: selectedParty?.name,
            entityType: selectedParty?.type,
            entityId: selectedParty?.id,
            party_type: partyType,
            is_client: isClient,
            internal_number: internalNumber,
            remarks: remarks,
            info: selectedParty?.info
          }">
    添加
  </button>
</mat-dialog-actions>
