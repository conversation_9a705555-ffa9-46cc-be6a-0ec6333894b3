.dialog-content {
  height: 500px;
  overflow-y: auto;
  overflow-x: hidden;
  padding-bottom: 16px;
}

.loading-spinner-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100px;
  margin: 20px 0;
}

.search-results-container {
  max-height: 200px;
  margin-bottom: 16px;

  /* 只有在有搜索内容时才设置最小高度 */
  &.has-content {
    min-height: 100px;
  }
}

.search-results-list {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.search-result-item {
  padding: 8px 16px;
  border-bottom: 1px solid #eee;
  cursor: pointer;

  &:hover {
    background-color: #f5f5f5;
  }

  &:last-child {
    border-bottom: none;
  }
}

.no-results {
  padding: 16px;
  text-align: center;
  color: #666;
  font-style: italic;
}

.selected-party {
  margin: 20px 0;
  padding: 10px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  background-color: #f5f5f5;

  .party-details p {
    margin: 5px 0;
  }
}

.party-role-section {
  margin-top: 16px;
}

.client-checkbox {
  display: block;
  margin: 15px 0;
}

.party-option {
  display: flex;
  align-items: center;

  .party-name {
    font-weight: 500;
  }

  .party-type-badge {
    margin-left: 8px;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.8em;

    &.natural {
      background-color: #2196f3;
      color: white;
    }

    &.legal {
      background-color: #ff9800;
      color: white;
    }
  }
}

.party-info {
  display: block;
  font-size: 0.8em;
  color: rgba(0,0,0,0.6);
}

/* 历史案件搜索结果样式 */
.legacy-results {
  margin-top: 20px;
  border-top: 1px solid #ddd;
  padding-top: 10px;

  .legacy-header {
    margin-bottom: 10px;
  }

  .legacy-result-item {
    padding: 8px 16px;
    border-bottom: 1px solid #eee;
  }

  .legacy-badge {
    margin-left: 8px;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.8em;
    background-color: #673ab7;
    color: white;
  }
}
